{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/taps/node_modules/.pnpm/next%4015.5.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/dist/client/components/builtin/global-error.js/__nextjs-internal-proxy.cjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nconst { createClientModuleProxy } = require(\"react-server-dom-turbopack/server\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/.pnpm/next@15.5.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/builtin/global-error.js <module evaluation>\"));\n"], "names": [], "mappings": "AAAA,uEAAuE;AACvE,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 9, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/taps/node_modules/.pnpm/next%4015.5.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/dist/client/components/builtin/global-error.js/__nextjs-internal-proxy.cjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nconst { createClientModuleProxy } = require(\"react-server-dom-turbopack/server\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/.pnpm/next@15.5.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/builtin/global-error.js\"));\n"], "names": [], "mappings": "AAAA,uEAAuE;AACvE,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/taps/node_modules/.pnpm/next%4015.5.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/client/components/builtin/global-error.tsx"], "sourcesContent": ["'use client'\n\nimport { HandleISRError } from '../handle-isr-error'\n\nconst styles = {\n  error: {\n    // https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L38-L52\n    fontFamily:\n      'system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"',\n    height: '100vh',\n    textAlign: 'center',\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n  text: {\n    fontSize: '14px',\n    fontWeight: 400,\n    lineHeight: '28px',\n    margin: '0 8px',\n  },\n} as const\n\nexport type GlobalErrorComponent = React.ComponentType<{\n  error: any\n}>\nfunction DefaultGlobalError({ error }: { error: any }) {\n  const digest: string | undefined = error?.digest\n  return (\n    <html id=\"__next_error__\">\n      <head></head>\n      <body>\n        <HandleISRError error={error} />\n        <div style={styles.error}>\n          <div>\n            <h2 style={styles.text}>\n              Application error: a {digest ? 'server' : 'client'}-side exception\n              has occurred while loading {window.location.hostname} (see the{' '}\n              {digest ? 'server logs' : 'browser console'} for more\n              information).\n            </h2>\n            {digest ? <p style={styles.text}>{`Digest: ${digest}`}</p> : null}\n          </div>\n        </div>\n      </body>\n    </html>\n  )\n}\n\n// Exported so that the import signature in the loaders can be identical to user\n// supplied custom global error signatures.\nexport default DefaultGlobalError\n"], "names": ["styles", "error", "fontFamily", "height", "textAlign", "display", "flexDirection", "alignItems", "justifyContent", "text", "fontSize", "fontWeight", "lineHeight", "margin", "DefaultGlobalError", "digest", "html", "id", "head", "body", "HandleISRError", "div", "style", "h2", "window", "location", "hostname", "p"], "mappings": "", "ignoreList": [0], "debugId": null}}]}