'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var client = require('framer-motion/client');



Object.defineProperty(exports, "a", {
	enumerable: true,
	get: function () { return client.a; }
});
Object.defineProperty(exports, "abbr", {
	enumerable: true,
	get: function () { return client.abbr; }
});
Object.defineProperty(exports, "address", {
	enumerable: true,
	get: function () { return client.address; }
});
Object.defineProperty(exports, "animate", {
	enumerable: true,
	get: function () { return client.animate; }
});
Object.defineProperty(exports, "area", {
	enumerable: true,
	get: function () { return client.area; }
});
Object.defineProperty(exports, "article", {
	enumerable: true,
	get: function () { return client.article; }
});
Object.defineProperty(exports, "aside", {
	enumerable: true,
	get: function () { return client.aside; }
});
Object.defineProperty(exports, "audio", {
	enumerable: true,
	get: function () { return client.audio; }
});
Object.defineProperty(exports, "b", {
	enumerable: true,
	get: function () { return client.b; }
});
Object.defineProperty(exports, "base", {
	enumerable: true,
	get: function () { return client.base; }
});
Object.defineProperty(exports, "bdi", {
	enumerable: true,
	get: function () { return client.bdi; }
});
Object.defineProperty(exports, "bdo", {
	enumerable: true,
	get: function () { return client.bdo; }
});
Object.defineProperty(exports, "big", {
	enumerable: true,
	get: function () { return client.big; }
});
Object.defineProperty(exports, "blockquote", {
	enumerable: true,
	get: function () { return client.blockquote; }
});
Object.defineProperty(exports, "body", {
	enumerable: true,
	get: function () { return client.body; }
});
Object.defineProperty(exports, "button", {
	enumerable: true,
	get: function () { return client.button; }
});
Object.defineProperty(exports, "canvas", {
	enumerable: true,
	get: function () { return client.canvas; }
});
Object.defineProperty(exports, "caption", {
	enumerable: true,
	get: function () { return client.caption; }
});
Object.defineProperty(exports, "circle", {
	enumerable: true,
	get: function () { return client.circle; }
});
Object.defineProperty(exports, "cite", {
	enumerable: true,
	get: function () { return client.cite; }
});
Object.defineProperty(exports, "clipPath", {
	enumerable: true,
	get: function () { return client.clipPath; }
});
Object.defineProperty(exports, "code", {
	enumerable: true,
	get: function () { return client.code; }
});
Object.defineProperty(exports, "col", {
	enumerable: true,
	get: function () { return client.col; }
});
Object.defineProperty(exports, "colgroup", {
	enumerable: true,
	get: function () { return client.colgroup; }
});
Object.defineProperty(exports, "create", {
	enumerable: true,
	get: function () { return client.create; }
});
Object.defineProperty(exports, "data", {
	enumerable: true,
	get: function () { return client.data; }
});
Object.defineProperty(exports, "datalist", {
	enumerable: true,
	get: function () { return client.datalist; }
});
Object.defineProperty(exports, "dd", {
	enumerable: true,
	get: function () { return client.dd; }
});
Object.defineProperty(exports, "defs", {
	enumerable: true,
	get: function () { return client.defs; }
});
Object.defineProperty(exports, "del", {
	enumerable: true,
	get: function () { return client.del; }
});
Object.defineProperty(exports, "desc", {
	enumerable: true,
	get: function () { return client.desc; }
});
Object.defineProperty(exports, "details", {
	enumerable: true,
	get: function () { return client.details; }
});
Object.defineProperty(exports, "dfn", {
	enumerable: true,
	get: function () { return client.dfn; }
});
Object.defineProperty(exports, "dialog", {
	enumerable: true,
	get: function () { return client.dialog; }
});
Object.defineProperty(exports, "div", {
	enumerable: true,
	get: function () { return client.div; }
});
Object.defineProperty(exports, "dl", {
	enumerable: true,
	get: function () { return client.dl; }
});
Object.defineProperty(exports, "dt", {
	enumerable: true,
	get: function () { return client.dt; }
});
Object.defineProperty(exports, "ellipse", {
	enumerable: true,
	get: function () { return client.ellipse; }
});
Object.defineProperty(exports, "em", {
	enumerable: true,
	get: function () { return client.em; }
});
Object.defineProperty(exports, "embed", {
	enumerable: true,
	get: function () { return client.embed; }
});
Object.defineProperty(exports, "feBlend", {
	enumerable: true,
	get: function () { return client.feBlend; }
});
Object.defineProperty(exports, "feColorMatrix", {
	enumerable: true,
	get: function () { return client.feColorMatrix; }
});
Object.defineProperty(exports, "feComponentTransfer", {
	enumerable: true,
	get: function () { return client.feComponentTransfer; }
});
Object.defineProperty(exports, "feComposite", {
	enumerable: true,
	get: function () { return client.feComposite; }
});
Object.defineProperty(exports, "feConvolveMatrix", {
	enumerable: true,
	get: function () { return client.feConvolveMatrix; }
});
Object.defineProperty(exports, "feDiffuseLighting", {
	enumerable: true,
	get: function () { return client.feDiffuseLighting; }
});
Object.defineProperty(exports, "feDisplacementMap", {
	enumerable: true,
	get: function () { return client.feDisplacementMap; }
});
Object.defineProperty(exports, "feDistantLight", {
	enumerable: true,
	get: function () { return client.feDistantLight; }
});
Object.defineProperty(exports, "feDropShadow", {
	enumerable: true,
	get: function () { return client.feDropShadow; }
});
Object.defineProperty(exports, "feFlood", {
	enumerable: true,
	get: function () { return client.feFlood; }
});
Object.defineProperty(exports, "feFuncA", {
	enumerable: true,
	get: function () { return client.feFuncA; }
});
Object.defineProperty(exports, "feFuncB", {
	enumerable: true,
	get: function () { return client.feFuncB; }
});
Object.defineProperty(exports, "feFuncG", {
	enumerable: true,
	get: function () { return client.feFuncG; }
});
Object.defineProperty(exports, "feFuncR", {
	enumerable: true,
	get: function () { return client.feFuncR; }
});
Object.defineProperty(exports, "feGaussianBlur", {
	enumerable: true,
	get: function () { return client.feGaussianBlur; }
});
Object.defineProperty(exports, "feImage", {
	enumerable: true,
	get: function () { return client.feImage; }
});
Object.defineProperty(exports, "feMerge", {
	enumerable: true,
	get: function () { return client.feMerge; }
});
Object.defineProperty(exports, "feMergeNode", {
	enumerable: true,
	get: function () { return client.feMergeNode; }
});
Object.defineProperty(exports, "feMorphology", {
	enumerable: true,
	get: function () { return client.feMorphology; }
});
Object.defineProperty(exports, "feOffset", {
	enumerable: true,
	get: function () { return client.feOffset; }
});
Object.defineProperty(exports, "fePointLight", {
	enumerable: true,
	get: function () { return client.fePointLight; }
});
Object.defineProperty(exports, "feSpecularLighting", {
	enumerable: true,
	get: function () { return client.feSpecularLighting; }
});
Object.defineProperty(exports, "feSpotLight", {
	enumerable: true,
	get: function () { return client.feSpotLight; }
});
Object.defineProperty(exports, "feTile", {
	enumerable: true,
	get: function () { return client.feTile; }
});
Object.defineProperty(exports, "feTurbulence", {
	enumerable: true,
	get: function () { return client.feTurbulence; }
});
Object.defineProperty(exports, "fieldset", {
	enumerable: true,
	get: function () { return client.fieldset; }
});
Object.defineProperty(exports, "figcaption", {
	enumerable: true,
	get: function () { return client.figcaption; }
});
Object.defineProperty(exports, "figure", {
	enumerable: true,
	get: function () { return client.figure; }
});
Object.defineProperty(exports, "filter", {
	enumerable: true,
	get: function () { return client.filter; }
});
Object.defineProperty(exports, "footer", {
	enumerable: true,
	get: function () { return client.footer; }
});
Object.defineProperty(exports, "foreignObject", {
	enumerable: true,
	get: function () { return client.foreignObject; }
});
Object.defineProperty(exports, "form", {
	enumerable: true,
	get: function () { return client.form; }
});
Object.defineProperty(exports, "g", {
	enumerable: true,
	get: function () { return client.g; }
});
Object.defineProperty(exports, "h1", {
	enumerable: true,
	get: function () { return client.h1; }
});
Object.defineProperty(exports, "h2", {
	enumerable: true,
	get: function () { return client.h2; }
});
Object.defineProperty(exports, "h3", {
	enumerable: true,
	get: function () { return client.h3; }
});
Object.defineProperty(exports, "h4", {
	enumerable: true,
	get: function () { return client.h4; }
});
Object.defineProperty(exports, "h5", {
	enumerable: true,
	get: function () { return client.h5; }
});
Object.defineProperty(exports, "h6", {
	enumerable: true,
	get: function () { return client.h6; }
});
Object.defineProperty(exports, "head", {
	enumerable: true,
	get: function () { return client.head; }
});
Object.defineProperty(exports, "header", {
	enumerable: true,
	get: function () { return client.header; }
});
Object.defineProperty(exports, "hgroup", {
	enumerable: true,
	get: function () { return client.hgroup; }
});
Object.defineProperty(exports, "hr", {
	enumerable: true,
	get: function () { return client.hr; }
});
Object.defineProperty(exports, "html", {
	enumerable: true,
	get: function () { return client.html; }
});
Object.defineProperty(exports, "i", {
	enumerable: true,
	get: function () { return client.i; }
});
Object.defineProperty(exports, "iframe", {
	enumerable: true,
	get: function () { return client.iframe; }
});
Object.defineProperty(exports, "image", {
	enumerable: true,
	get: function () { return client.image; }
});
Object.defineProperty(exports, "img", {
	enumerable: true,
	get: function () { return client.img; }
});
Object.defineProperty(exports, "input", {
	enumerable: true,
	get: function () { return client.input; }
});
Object.defineProperty(exports, "ins", {
	enumerable: true,
	get: function () { return client.ins; }
});
Object.defineProperty(exports, "kbd", {
	enumerable: true,
	get: function () { return client.kbd; }
});
Object.defineProperty(exports, "keygen", {
	enumerable: true,
	get: function () { return client.keygen; }
});
Object.defineProperty(exports, "label", {
	enumerable: true,
	get: function () { return client.label; }
});
Object.defineProperty(exports, "legend", {
	enumerable: true,
	get: function () { return client.legend; }
});
Object.defineProperty(exports, "li", {
	enumerable: true,
	get: function () { return client.li; }
});
Object.defineProperty(exports, "line", {
	enumerable: true,
	get: function () { return client.line; }
});
Object.defineProperty(exports, "linearGradient", {
	enumerable: true,
	get: function () { return client.linearGradient; }
});
Object.defineProperty(exports, "link", {
	enumerable: true,
	get: function () { return client.link; }
});
Object.defineProperty(exports, "main", {
	enumerable: true,
	get: function () { return client.main; }
});
Object.defineProperty(exports, "map", {
	enumerable: true,
	get: function () { return client.map; }
});
Object.defineProperty(exports, "mark", {
	enumerable: true,
	get: function () { return client.mark; }
});
Object.defineProperty(exports, "marker", {
	enumerable: true,
	get: function () { return client.marker; }
});
Object.defineProperty(exports, "mask", {
	enumerable: true,
	get: function () { return client.mask; }
});
Object.defineProperty(exports, "menu", {
	enumerable: true,
	get: function () { return client.menu; }
});
Object.defineProperty(exports, "menuitem", {
	enumerable: true,
	get: function () { return client.menuitem; }
});
Object.defineProperty(exports, "metadata", {
	enumerable: true,
	get: function () { return client.metadata; }
});
Object.defineProperty(exports, "meter", {
	enumerable: true,
	get: function () { return client.meter; }
});
Object.defineProperty(exports, "nav", {
	enumerable: true,
	get: function () { return client.nav; }
});
Object.defineProperty(exports, "object", {
	enumerable: true,
	get: function () { return client.object; }
});
Object.defineProperty(exports, "ol", {
	enumerable: true,
	get: function () { return client.ol; }
});
Object.defineProperty(exports, "optgroup", {
	enumerable: true,
	get: function () { return client.optgroup; }
});
Object.defineProperty(exports, "option", {
	enumerable: true,
	get: function () { return client.option; }
});
Object.defineProperty(exports, "output", {
	enumerable: true,
	get: function () { return client.output; }
});
Object.defineProperty(exports, "p", {
	enumerable: true,
	get: function () { return client.p; }
});
Object.defineProperty(exports, "param", {
	enumerable: true,
	get: function () { return client.param; }
});
Object.defineProperty(exports, "path", {
	enumerable: true,
	get: function () { return client.path; }
});
Object.defineProperty(exports, "pattern", {
	enumerable: true,
	get: function () { return client.pattern; }
});
Object.defineProperty(exports, "picture", {
	enumerable: true,
	get: function () { return client.picture; }
});
Object.defineProperty(exports, "polygon", {
	enumerable: true,
	get: function () { return client.polygon; }
});
Object.defineProperty(exports, "polyline", {
	enumerable: true,
	get: function () { return client.polyline; }
});
Object.defineProperty(exports, "pre", {
	enumerable: true,
	get: function () { return client.pre; }
});
Object.defineProperty(exports, "progress", {
	enumerable: true,
	get: function () { return client.progress; }
});
Object.defineProperty(exports, "q", {
	enumerable: true,
	get: function () { return client.q; }
});
Object.defineProperty(exports, "radialGradient", {
	enumerable: true,
	get: function () { return client.radialGradient; }
});
Object.defineProperty(exports, "rect", {
	enumerable: true,
	get: function () { return client.rect; }
});
Object.defineProperty(exports, "rp", {
	enumerable: true,
	get: function () { return client.rp; }
});
Object.defineProperty(exports, "rt", {
	enumerable: true,
	get: function () { return client.rt; }
});
Object.defineProperty(exports, "ruby", {
	enumerable: true,
	get: function () { return client.ruby; }
});
Object.defineProperty(exports, "s", {
	enumerable: true,
	get: function () { return client.s; }
});
Object.defineProperty(exports, "samp", {
	enumerable: true,
	get: function () { return client.samp; }
});
Object.defineProperty(exports, "script", {
	enumerable: true,
	get: function () { return client.script; }
});
Object.defineProperty(exports, "section", {
	enumerable: true,
	get: function () { return client.section; }
});
Object.defineProperty(exports, "select", {
	enumerable: true,
	get: function () { return client.select; }
});
Object.defineProperty(exports, "small", {
	enumerable: true,
	get: function () { return client.small; }
});
Object.defineProperty(exports, "source", {
	enumerable: true,
	get: function () { return client.source; }
});
Object.defineProperty(exports, "span", {
	enumerable: true,
	get: function () { return client.span; }
});
Object.defineProperty(exports, "stop", {
	enumerable: true,
	get: function () { return client.stop; }
});
Object.defineProperty(exports, "strong", {
	enumerable: true,
	get: function () { return client.strong; }
});
Object.defineProperty(exports, "style", {
	enumerable: true,
	get: function () { return client.style; }
});
Object.defineProperty(exports, "sub", {
	enumerable: true,
	get: function () { return client.sub; }
});
Object.defineProperty(exports, "summary", {
	enumerable: true,
	get: function () { return client.summary; }
});
Object.defineProperty(exports, "sup", {
	enumerable: true,
	get: function () { return client.sup; }
});
Object.defineProperty(exports, "svg", {
	enumerable: true,
	get: function () { return client.svg; }
});
Object.defineProperty(exports, "symbol", {
	enumerable: true,
	get: function () { return client.symbol; }
});
Object.defineProperty(exports, "table", {
	enumerable: true,
	get: function () { return client.table; }
});
Object.defineProperty(exports, "tbody", {
	enumerable: true,
	get: function () { return client.tbody; }
});
Object.defineProperty(exports, "td", {
	enumerable: true,
	get: function () { return client.td; }
});
Object.defineProperty(exports, "text", {
	enumerable: true,
	get: function () { return client.text; }
});
Object.defineProperty(exports, "textPath", {
	enumerable: true,
	get: function () { return client.textPath; }
});
Object.defineProperty(exports, "textarea", {
	enumerable: true,
	get: function () { return client.textarea; }
});
Object.defineProperty(exports, "tfoot", {
	enumerable: true,
	get: function () { return client.tfoot; }
});
Object.defineProperty(exports, "th", {
	enumerable: true,
	get: function () { return client.th; }
});
Object.defineProperty(exports, "thead", {
	enumerable: true,
	get: function () { return client.thead; }
});
Object.defineProperty(exports, "time", {
	enumerable: true,
	get: function () { return client.time; }
});
Object.defineProperty(exports, "title", {
	enumerable: true,
	get: function () { return client.title; }
});
Object.defineProperty(exports, "tr", {
	enumerable: true,
	get: function () { return client.tr; }
});
Object.defineProperty(exports, "track", {
	enumerable: true,
	get: function () { return client.track; }
});
Object.defineProperty(exports, "tspan", {
	enumerable: true,
	get: function () { return client.tspan; }
});
Object.defineProperty(exports, "u", {
	enumerable: true,
	get: function () { return client.u; }
});
Object.defineProperty(exports, "ul", {
	enumerable: true,
	get: function () { return client.ul; }
});
Object.defineProperty(exports, "use", {
	enumerable: true,
	get: function () { return client.use; }
});
Object.defineProperty(exports, "video", {
	enumerable: true,
	get: function () { return client.video; }
});
Object.defineProperty(exports, "view", {
	enumerable: true,
	get: function () { return client.view; }
});
Object.defineProperty(exports, "wbr", {
	enumerable: true,
	get: function () { return client.wbr; }
});
Object.defineProperty(exports, "webview", {
	enumerable: true,
	get: function () { return client.webview; }
});
